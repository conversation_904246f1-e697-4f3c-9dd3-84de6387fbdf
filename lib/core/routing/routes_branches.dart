import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gather_point/core/routing/routes_keys.dart';
import 'package:gather_point/core/services/service_locator.dart';
import 'package:gather_point/core/utils/app_constants.dart';
import 'package:gather_point/feature/auth/Domain/Entities/user_entity.dart';
import 'package:gather_point/feature/favorite/presentation/views/favorite_screen.dart';
import 'package:gather_point/feature/home/<USER>/views/home_screen.dart';
import 'package:gather_point/feature/host/presentation/views/host_home_page.dart';
import 'package:gather_point/feature/host/presentation/views/host_reservations_page.dart';
import 'package:gather_point/feature/host/presentation/views/my_listings_page_simple.dart';
import 'package:gather_point/feature/host/presentation/cubit/my_listings_cubit.dart';
import 'package:gather_point/feature/host/presentation/cubit/host_dashboard_cubit.dart';
import 'package:gather_point/feature/host/data/services/my_listings_api_service.dart';
import 'package:gather_point/feature/host/data/services/host_api_service.dart';
import 'package:gather_point/feature/profile/presentation/views/profile_view.dart';
import 'package:gather_point/feature/reels/presentation/views/reels_page.dart';
import 'package:gather_point/feature/messages/presentation/views/messages_tab_page.dart';
import 'package:go_router/go_router.dart';
import 'package:hive/hive.dart';

// Global hoster mode notifier - single source of truth
final ValueNotifier<bool> isHosterModeNotifier = ValueNotifier(false);

// Initialize the notifier with current user's hoster mode status
void initializeHosterModeNotifier() {
  try {
    final userBox = Hive.box<UserEntity>(AppConstants.kMyProfileBoxName);
    final currentUser = userBox.get(AppConstants.kMyProfileKey);
    isHosterModeNotifier.value = currentUser?.isHosterMode ?? false;
    debugPrint(
        '🔧 HosterModeNotifier initialized with value: ${isHosterModeNotifier.value}');
  } catch (e) {
    debugPrint('⚠️ Error initializing HosterModeNotifier: $e');
    isHosterModeNotifier.value = false;
  }
}

List<StatefulShellBranch> routesBranches() {
  return [
    // Tab 0: Home
    StatefulShellBranch(
      routes: <RouteBase>[
        GoRoute(
          path: RoutesKeys.kHomeViewTab,
          builder: (context, state) => ValueListenableBuilder<bool>(
            valueListenable: isHosterModeNotifier,
            builder: (context, isHosterMode, child) {
              return isHosterMode
                  ? const HostHomePage()
                  : const GatherPointHome();
            },
          ),
        ),
      ],
    ),
    // Tab 1: Reels (Client mode only)
    StatefulShellBranch(
      routes: <RouteBase>[
        GoRoute(
          path: RoutesKeys.kReelsViewTab,
          builder: (context, state) => ValueListenableBuilder<bool>(
            valueListenable: isHosterModeNotifier,
            builder: (context, isHosterMode, child) {
              return isHosterMode
                  ? BlocProvider(
                      create: (context) =>
                          HostDashboardCubit(getIt<HostApiService>())
                            ..loadHostReservations(),
                      child: const HostReservationsPage(),
                    )
                  : const ReelsPage(
                      searchResults: [],
                      searchQuery: '',
                      serviceCategoryId: 1,
                      showBackButton: false,
                    );
            },
          ),
        ),
      ],
    ),
    // Tab 2: Favorites/Bookings
    StatefulShellBranch(
      routes: <RouteBase>[
        GoRoute(
          path: RoutesKeys.kFavoritesViewTab,
          builder: (context, state) => ValueListenableBuilder<bool>(
            valueListenable: isHosterModeNotifier,
            builder: (context, isHosterMode, child) {
              return isHosterMode
                  ? BlocProvider(
                      create: (context) => MyListingsCubit(
                        getIt<MyListingsApiService>(),
                      ),
                      child: const MyListingsPageSimple(),
                    )
                  : const FavoriteScreen();
            },
          ),
        ),
      ],
    ),
    // Tab 3: Bookings (Client) / Messages (Hoster)
    StatefulShellBranch(
      routes: <RouteBase>[
        GoRoute(
          path: RoutesKeys.kBookingsViewTab,
          builder: (context, state) => ValueListenableBuilder<bool>(
            valueListenable: isHosterModeNotifier,
            builder: (context, isHosterMode, child) {
              return isHosterMode
                  ? const MessagesTabPage()
                  : BlocProvider(
                      create: (context) =>
                          HostDashboardCubit(getIt<HostApiService>())
                            ..loadHostReservations(),
                      child: const HostReservationsPage(),
                    );
            },
          ),
        ),
      ],
    ),
    // Tab 4: Messages (Client) / Profile (Hoster)
    StatefulShellBranch(
      routes: <RouteBase>[
        GoRoute(
          path: RoutesKeys.kMessagesViewTab,
          builder: (context, state) => ValueListenableBuilder<bool>(
            valueListenable: isHosterModeNotifier,
            builder: (context, isHosterMode, child) {
              return isHosterMode
                  ? const ProfileTabView()
                  : const MessagesTabPage();
            },
          ),
        ),
      ],
    ),
    // Tab 5: Profile (Client mode only)
    StatefulShellBranch(
      routes: <RouteBase>[
        GoRoute(
          path: RoutesKeys.kProfileViewTab,
          builder: (context, state) => const ProfileTabView(),
        ),
      ],
    ),
  ];
}

ValueListenableBuilder<bool> buildRoutesBranches() {
  return ValueListenableBuilder<bool>(
    valueListenable: isHosterModeNotifier,
    builder: (context, isHosterMode, child) {
      return Container(); // Replace with an appropriate widget or UI representation
    },
  );
}
