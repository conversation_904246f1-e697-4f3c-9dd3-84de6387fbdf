// ignore_for_file: prefer_const_constructors, use_build_context_synchronously

import 'package:flutter/material.dart';
import 'package:gather_point/core/navigation_bar/floating_reels_navigation_bar.dart';
import 'package:go_router/go_router.dart';

class MainNavigationBar extends StatelessWidget {
  const MainNavigationBar({super.key, required this.navigationShell});

  final StatefulNavigationShell navigationShell;

  @override
  Widget build(BuildContext context) {
    // Use the new floating reels navigation bar
    return FloatingReelsNavigationBar(
      navigationShell: navigationShell,
      currentIndex: navigationShell.currentIndex,
    );
  }
}