// ignore_for_file: prefer_const_constructors, use_build_context_synchronously

import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:gather_point/core/routing/routes_branches.dart';
import 'package:gather_point/core/styles/theme_helper.dart';
import 'package:gather_point/core/utils/app_assets.dart';
import 'package:gather_point/core/utils/sound_manager.dart';
import 'package:gather_point/generated/l10n.dart';
import 'package:go_router/go_router.dart';

class FloatingReelsNavigationBar extends StatelessWidget {
  const FloatingReelsNavigationBar({
    super.key,
    required this.navigationShell,
    required this.currentIndex,
  });

  final StatefulNavigationShell navigationShell;
  final int currentIndex;

  @override
  Widget build(BuildContext context) {
    final s = S.of(context);
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return ValueListenableBuilder<bool>(
      valueListenable: isHosterModeNotifier,
      builder: (context, isHosterMode, child) {
        return Scaffold(
          body: navigationShell,
          bottomNavigationBar: Stack(
            children: [
              _buildBottomNavigationBar(context, s, isHosterMode, isDark),
              if (!isHosterMode) _buildReelsFloatingButton(context, s),
            ],
          ),
        );
      },
    );
  }

  Widget _buildReelsFloatingButton(BuildContext context, S s) {
    final isReelsActive = currentIndex == 1; // Reels tab index
    final isRTL = Directionality.of(context) == TextDirection.rtl;

    return Positioned(
      top: 5, // Position from top of the navigation bar
      left: isRTL ? null : 15, // Position at start for LTR
      right: isRTL ? 15 : null, // Position at start for RTL
      child: Container(
        width: 60,
        height: 60,
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          gradient: LinearGradient(
            colors: isReelsActive
                ? [
                    context.accentColor,
                    context.accentColor.withValues(alpha: 0.8)
                  ]
                : [
                    context.accentColor.withValues(alpha: 0.9),
                    context.accentColor.withValues(alpha: 0.7)
                  ],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
          boxShadow: [
            BoxShadow(
              color: context.accentColor.withValues(alpha: 0.4),
              blurRadius: 15,
              offset: const Offset(0, 5),
            ),
          ],
        ),
        child: Material(
          color: Colors.transparent,
          child: InkWell(
            borderRadius: BorderRadius.circular(30),
            onTap: () => _onReelsPressed(context),
            child: SizedBox(
              width: 60,
              height: 60,
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Image.asset(
                    AppAssets.iconsReels,
                    width: 22,
                    height: 22,
                    color: Colors.black,
                  ),
                  const SizedBox(height: 2),
                  Text(
                    s.reels,
                    style: TextStyle(
                      fontSize: 8,
                      fontWeight: FontWeight.w600,
                      color: Colors.black,
                    ),
                    textAlign: TextAlign.center,
                    maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ]),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildBottomNavigationBar(
      BuildContext context, S s, bool isHosterMode, bool isDark) {
    return Container(
      height: 70,
      decoration: BoxDecoration(
        color: context.backgroundColor,
        borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
        boxShadow: [
          BoxShadow(
            color: isDark
                ? Colors.black.withValues(alpha: 0.3)
                : Colors.grey.withValues(alpha: 0.2),
            blurRadius: 10,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: SafeArea(
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 8),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: _buildNavigationItems(context, s, isHosterMode),
          ),
        ),
      ),
    );
  }

  List<Widget> _buildNavigationItems(
      BuildContext context, S s, bool isHosterMode) {
    if (isHosterMode) {
      return [
        _buildNavItem(context, 0, AppAssets.iconsHome, s.home, false),
        _buildNavItem(context, 1, AppAssets.iconsCalendar, s.myBookings, true),
        _buildNavItem(context, 2, null, s.myListings, false,
            icon: Icons.home_work_outlined),
        _buildNavItem(context, 3, null, s.messages, false,
            icon: Icons.message_outlined),
        _buildNavItem(context, 4, AppAssets.iconsProfile, s.profile, true),
      ];
    } else {
      List<Widget> items = [
        _buildNavItem(context, 0, AppAssets.iconsHome, s.explore, false),
        _buildNavItem(context, 2, AppAssets.iconsFavorite, s.favorite, true),
        _buildNavItem(context, 3, AppAssets.iconsCalendar, s.myBookings, true),
        _buildNavItem(context, 4, null, s.messages, false,
            icon: Icons.message_outlined),
        _buildNavItem(context, 5, AppAssets.iconsProfile, s.profile, true),
      ];
      // Add space for floating button at the start position
      items.insert(0, const SizedBox(width: 70));
      return items;
    }
  }

  Widget _buildNavItem(
    BuildContext context,
    int index,
    String? assetPath,
    String title,
    bool isSvg, {
    IconData? icon,
  }) {
    final isSelected = currentIndex == index;

    return Flexible(
      child: InkWell(
        onTap: () => _onNavItemPressed(context, index),
        borderRadius: BorderRadius.circular(12),
        child: Container(
          padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 4),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              if (icon != null)
                Icon(
                  icon,
                  size: 20,
                  color: isSelected
                      ? context.accentColor
                      : context.secondaryTextColor,
                )
              else if (assetPath != null)
                ColorFiltered(
                  colorFilter: ColorFilter.mode(
                    isSelected
                        ? context.accentColor
                        : context.secondaryTextColor,
                    BlendMode.srcIn,
                  ),
                  child: isSvg
                      ? SvgPicture.asset(assetPath, width: 20, height: 20)
                      : Image.asset(assetPath, width: 20, height: 20),
                ),
              const SizedBox(height: 4),
              Text(
                title,
                style: TextStyle(
                  fontSize: 10,
                  fontWeight: isSelected ? FontWeight.w600 : FontWeight.w400,
                  color: isSelected
                      ? context.accentColor
                      : context.secondaryTextColor,
                ),
                textAlign: TextAlign.center,
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _onNavItemPressed(BuildContext context, int index) {
    try {
      SoundManager.playClickSound().catchError((e) {
        debugPrint('Sound error: $e');
      });

      if (context.mounted) {
        navigationShell.goBranch(index);
      }
    } catch (e) {
      debugPrint('Navigation error: $e');
    }
  }

  void _onReelsPressed(BuildContext context) {
    try {
      SoundManager.playClickSound().catchError((e) {
        debugPrint('Sound error: $e');
      });

      if (context.mounted) {
        navigationShell.goBranch(1); // Navigate to reels tab (index 1)
      }
    } catch (e) {
      debugPrint('Reels navigation error: $e');
    }
  }
}
