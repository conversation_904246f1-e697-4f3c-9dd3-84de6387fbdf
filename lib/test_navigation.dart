import 'package:flutter/material.dart';
import 'package:gather_point/core/navigation_bar/floating_reels_navigation_bar.dart';
import 'package:gather_point/core/routing/routes_branches.dart';
import 'package:gather_point/generated/l10n.dart';
import 'package:go_router/go_router.dart';

// Test file to preview the navigation bar layout
class TestNavigationPreview extends StatelessWidget {
  const TestNavigationPreview({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      home: Scaffold(
        appBar: AppBar(
          title: const Text('Navigation Test'),
          backgroundColor: const Color(0xFFFEC53A),
        ),
        body: const Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                'Navigation Bar Test',
                style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
              ),
              SizedBox(height: 20),
              Text(
                'Check the bottom navigation bar layout',
                style: TextStyle(fontSize: 16),
              ),
            ],
          ),
        ),
        bottomNavigationBar: _buildTestBottomNav(context),
        floatingActionButton: _buildTestFloatingButton(context),
        floatingActionButtonLocation: FloatingActionButtonLocation.centerFloat,
      ),
    );
  }

  Widget _buildTestBottomNav(BuildContext context) {
    return Container(
      height: 70,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.2),
            blurRadius: 10,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: SafeArea(
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 8),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              _buildTestNavItem('Home', Icons.home, true),
              _buildTestNavItem('Favorites', Icons.favorite, false),
              const SizedBox(width: 60), // Space for floating button
              _buildTestNavItem('Bookings', Icons.calendar_today, false),
              _buildTestNavItem('Messages', Icons.message, false),
              _buildTestNavItem('Profile', Icons.person, false),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildTestNavItem(String title, IconData icon, bool isSelected) {
    return Flexible(
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 4),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              icon,
              size: 20,
              color: isSelected ? const Color(0xFFFEC53A) : Colors.grey,
            ),
            const SizedBox(height: 4),
            Text(
              title,
              style: TextStyle(
                fontSize: 10,
                fontWeight: isSelected ? FontWeight.w600 : FontWeight.w400,
                color: isSelected ? const Color(0xFFFEC53A) : Colors.grey,
              ),
              textAlign: TextAlign.center,
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTestFloatingButton(BuildContext context) {
    return Container(
      width: 60,
      height: 60,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        gradient: const LinearGradient(
          colors: [Color(0xFFFEC53A), Color(0xFFE6B02E)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        boxShadow: [
          BoxShadow(
            color: const Color(0xFFFEC53A).withValues(alpha: 0.4),
            blurRadius: 15,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(30),
          onTap: () {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(content: Text('Reels button pressed!')),
            );
          },
          child: const Center(
            child: Icon(
              Icons.play_arrow_rounded,
              color: Colors.black,
              size: 28,
            ),
          ),
        ),
      ),
    );
  }
}
